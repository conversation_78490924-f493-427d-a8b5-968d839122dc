/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.negocio;

import co.venko.ms.models.entity.AdmUsuario;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.EndpointDTO;
import se.firme.commons.firmese.dto.PersonaScanDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.firmese.service.*;
import se.firme.commons.firmese.util.Utilities;
import se.firme.commons.models.projection.IUsuario;
import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.datos.models.entity.TipoDocumento;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.ServicioService;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.UsuarioServiceImpl;
import se.firme.ms.models.service.ValidacionFuenteServiceImpl;
import se.firme.ms.models.service.helper.UsuarioHelper;
import se.firme.ms.models.service.interfaz.IBitacoraRegistroService;
import se.firme.ms.models.service.interfaz.IParametroService;
import se.firme.ms.usuario.rest.client.UsuarioVMSClient;

/**
 *
 * <AUTHOR>
 */
@Service
public class UsuarioNegocio implements IUsuarioNegosioService {

    @Autowired
    private TokenServiceImpl tokenServiceImpl;

    @Autowired
    private UsuarioServiceImpl usuarioService;

    @Autowired
    private IBitacoraRegistroService bitacoraService;

    @Autowired
    private UsuarioVMSClient usuarioVMSClient;

    @Autowired
    IParametroService parametroService;

    @Autowired
    private ValidacionFuenteServiceImpl validaiconFuenteService;

    @Autowired
    private ServicioService iServicioService;

    @Autowired
    private Environment env;
    @Autowired
    private PasswordEncoder passwordEncoder;

    private static Logger logger = Logger.getLogger(UsuarioNegocio.class.getName());

    @Override
    @Transactional
    public void procesoRegistro(RegistroDTO datosRegistro) throws FirmaException {
        try {
            // verificar si existe el usuario en proceso de registro
            List<Usuario> u = usuarioService.getVerificarUsuarioRegistro(datosRegistro.getNumeroDocumento(),
                    datosRegistro.getCorreoElectronico(), datosRegistro.getNumeroCelular());

            boolean continuarRegistro = true;
            Token token = null;
            Usuario usuario = new Usuario();
            Usuario usuarioCreado = null;
            if (u != null && !u.isEmpty()) {
                usuario = u.get(0);
                usuarioCreado = usuario;

                if (!usuario.getProcesoRegistro()) {
                    continuarRegistro = false;
                } else {
                    token = tokenServiceImpl.getTokenActivoUsuario(usuario);
                }
            } else {
                usuario.setClave(datosRegistro.getContrasena());
                usuario.setNumeroDocumento(datosRegistro.getNumeroDocumento());
                usuario.setFechaExpedicionDocumento(
                        Utilities.getFechaTextoADate(datosRegistro.getFechaExpedicion(), "yyyy-MM-dd"));
                usuario.setCorreoElectronico(datosRegistro.getCorreoElectronico());
                usuario.setNumeroCelular(datosRegistro.getNumeroCelular());
                usuario.setIdTipoDocumento(new TipoDocumento(datosRegistro.getTipoDocumento()));
                usuario.setEstado(true);
                usuarioCreado = usuarioService.crearRegistroUsuario(usuario);

                token = tokenServiceImpl.getTokenActivoUsuario(usuarioCreado);
            }

            if (continuarRegistro) {
                bitacoraService.registrarBitacora(usuarioCreado);
                // token = tokenServiceImpl.getNuevoToken(usuarioCreado,
                // Parameters.string.TOKEN_TIPO_REGISTRO_USUARIO, 30, null);
                if (token != null) {

                    EmailService.send(datosRegistro.getCorreoElectronico(), "Activación de cuenta", EmailTemplateService
                            .registrationEmailTemplate(token.getIdToken(), env.getProperty("app.web.frontend")));
                    validarUsuarioRegistraduria(usuario);
                }
            } else {
                throw new Exception(
                        "El usuario con el correo electrónico o número de documento o número de celular indicado ya se encuentra registrado ");
            }

        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    private void validarUsuarioRegistraduria(Usuario usuario) {
        try {
            new Thread(() -> {
                try {
                    EndpointDTO endpointDTO = new EndpointDTO();
                    endpointDTO.setUrl(env.getProperty("routes.custom.konivin.ws"));
                    endpointDTO.setUser(env.getProperty("routes.custom.konivin.pass"));
                    endpointDTO.setPasswd(env.getProperty("routes.custom.konivin.user"));
                    validaiconFuenteService.validarUsuarioEnRegistraduria(usuario, endpointDTO);
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
                }
            }).start();
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
        }
    }

    @Override
    public boolean completarRegistro(DocumentoDTO documento) throws FirmaException {
        try {
            Token token = tokenServiceImpl.consultarCodTransaccion(documento.getCodTransaccion());
            if (token != null) {
                Usuario usuario = token.getIdUsuario();
                if (usuario != null) {
                    logger.log(Level.INFO, "Fecha expedición registro: {0}", usuario.getFechaExpedicionDocumento());
                    String json = Utilities.converB64ToString(documento.getDocument());
                    logger.log(Level.INFO, "Entrante: {0}", json);
                    json = json.substring(json.indexOf("{"), json.indexOf("}") + 1);
                    logger.log(Level.INFO, "Reparado: {0}", json);
                    PersonaScanDTO personaDTO = new Gson().fromJson(json, PersonaScanDTO.class);
                    logger.log(Level.INFO, "Json convertido a objeto");
                    if (personaDTO != null) {
                        logger.log(Level.INFO, "Valida número de cédula: {0}", personaDTO.getDocumentID());
                        logger.log(Level.INFO, "RawData: {0}", Utilities.converB64ToString(documento.getRowData()));
                        String documentoRegistro = "";
                        try {
                            documentoRegistro = String.valueOf(Integer.parseInt(personaDTO.getDocumentID()));
                        } catch (Exception e) {
                            documentoRegistro = personaDTO.getDocumentID();
                        }

                        if (documentoRegistro.equals(usuario.getNumeroDocumento())) {
                            usuario.setEstado(true);
                            usuario.setActivo(true);
                            usuario.setProcesoRegistro(false);
                            usuario.setRowData(documento.getRowData());
                            usuario.setDocumentoPersona(documento.getDocument());
                            usuario.setObervacionVerificacion("Usuario Verificado");
                            if (Utilities.isVacio(usuario.getNombreCompleto())) {
                                usuario.setNombreCompleto(personaDTO.getFirstName() + " " + personaDTO.getSecondName()
                                        + " " + personaDTO.getSurename() + " " + personaDTO.getSecondSurename());
                            }
                            usuarioService.editarRegistro(usuario);
                            logger.log(Level.INFO, "Registro usuario actualizado: {0}", documentoRegistro);
                            AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                            usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                            logger.log(Level.INFO, "User admin activo: {0}", documentoRegistro);
                            tokenServiceImpl.desactivarToken(documento.getCodTransaccion());
                            logger.log(Level.INFO, "Token desactivado: {0}", documentoRegistro);
                            return true;
                        }
                        throw new FirmaException("No coincide el número de documento de la persona");
                    }
                    throw new FirmaException("No fue posible obtener el registro de personaDTO");
                }
                throw new FirmaException("No fue posible obtener el registro de usuario");
            }
            throw new FirmaException("No fue posible obtener el registro de token");
        } catch (FirmaException | NullPointerException | IOException | JsonSyntaxException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Override
    public ServicioDTO getUsuarioServicio(long idUsuario) throws FirmaException {
        try {
            ServicioDTO servicio = iServicioService.findById(idUsuario);
            return servicio;
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Override
    public Object agregarPaqueteServicio(String json) throws FirmaException {
        try {
            JsonElement element = new JsonParser().parse(json);
            if (element != null) {
                JsonObject jsonObject = element.getAsJsonObject();
                if (jsonObject != null) {
                    String sku = jsonObject.get("id_sku").getAsString();
                    if (sku != null && !"".equals(sku)) {
                        if (iServicioService.agregarPaqueteServicio(jsonObject.get("codigo").getAsInt(),
                                jsonObject.get("id_sku").getAsInt())) {
                            return "Paquete agregado";
                        }
                    } else {
                        if (iServicioService.agregarPaqueteServicio(jsonObject.get("codigo").getAsInt(),
                                jsonObject.get("cantidad").getAsInt(), jsonObject.get("vigencia").getAsInt(),
                                jsonObject.get("otros").getAsInt())) {
                            return "Paquete agregado";
                        }
                    }
                    return "No se agregó el paquete";
                }
            }
            throw new FirmaException("No se do convertir el json: " + json);
        } catch (Exception e) {
            throw new FirmaException("Error: " + e.getMessage());
        }
    }

    @Override
    public boolean cambiarContrasena(long usuario, String json) throws FirmaException {
        try {
            JsonElement element = new JsonParser().parse(json);
            if (element != null) {
                JsonObject jsonObject = element.getAsJsonObject();
                if (jsonObject != null) {
                    Usuario dto = usuarioService.findById(usuario);
                    if (dto != null) {
                        String passwd = jsonObject.get("passwd").getAsString();
                        AdmUsuario admUsuario = UsuarioHelper.convert(dto);
                        admUsuario.setClaveUsuario(passwordEncoder.encode(passwd));
                        usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                        String ctoken = jsonObject.get("ctc").getAsString();
                        tokenServiceImpl.desactivarToken(ctoken);
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            throw new FirmaException("Error: " + e.getMessage());
        }
    }

    @Override
    public IUsuario consultarUsuario(String id) {
        List<IUsuario> lista = usuarioService.consultarUsuario(id);
        if (lista != null && !lista.isEmpty()) {
            return lista.get(0);
        }
        return null;
    }

    public boolean actualizarServicio(ServicioDTO dto) throws FirmaException {
        try {
            String endpoint = null;

            if (dto.getEndpointCBack() != null && !"".equals(dto.getEndpointCBack().trim())) {
                byte[] array = Base64.getDecoder().decode(dto.getEndpointCBack());
                endpoint = new String(array);
            }

            iServicioService.actualizarServicio(dto.getIdServicio(), endpoint, dto.isEndpointCBackHabilitado());

            iServicioService.actualizarNotificarFirma(dto.getIdServicio(), dto.isNotificarFirma());

            return true;
        } catch (Exception e) {
            throw new FirmaException("Error: " + e.getMessage());
        }
    }

    public boolean eliminarCuenta(int idUsuario) throws FirmaException {
        try {
            System.out.println("Eliminando cuenta de usuario con id: " + idUsuario);
            if (usuarioService.eliminarCuenta(idUsuario)) {
                //	usuarioVMSClient.eliminarUsuarioAdm(idUsuario+"");
                System.out.println("Eliminación de usuario adm: ");
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new FirmaException("No se pudo eliminar usuario: " + e.getMessage());
        }
    }

    public IUsuarioOris findByNumeroDeDocumentoOEmail(String docOrEmail) throws FirmaException {
        try {
            return usuarioService.findByNumeroDocumentoOrCorreoElectronico(docOrEmail);
        } catch (Exception e) {
            e.printStackTrace();
            throw new FirmaException("Ocurrió un error al buscar el usuario");
        }
    }
}
