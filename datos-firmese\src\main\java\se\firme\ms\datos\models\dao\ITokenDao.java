/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.Token;

/**
 *
 * <AUTHOR>
 */
public interface ITokenDao extends PagingAndSortingRepository<Token, String> {

    @Query(value = "select * from token where id_usuario = ?1 and activo = 1 ", nativeQuery = true)
    public List<Token> findTokenActivoUsuario(long idUsuario);

    @Query(value = "select * from token where  id_token = ?1", nativeQuery = true)
    public Token findByCode(String code);

    @Query(value = "update token set codigo_sms = ?1 where   codigo_transaccion = ?2", nativeQuery = true)
    @Modifying
    public void addCodeSms(String codigoSMS, String codigo_transaccion);

    @Query(value = "update token set activo = false where   codigo_transaccion = ?1", nativeQuery = true)
    @Modifying
    public void desactivarToken(String ctoken);
    
    @Query(value = "update token set activo = 0 where id_token = ?1", nativeQuery = true)
    @Modifying
    public void desactivarTokenByIdToken(String idToken);
    
    @Query(value = "select * from token where  codigo_transaccion = ?1 and activo = 1", nativeQuery = true)
    public Token findByCodTransaccion(String codTransaccion);
    
    @Query(value = "select * from token where id_archivo_firma = ?1 and activo = 1 and id_usuario = ?2 and tipo = 2 order by fecha_vencimiento desc limit 1", nativeQuery = true)
    public Token findByIdArchivoFirma(long idArchivoFirma, long idUsuario);
    
    @Query(value = "select * from token where codigo_transaccion = ?1 and activo = 1 and tipo = 2 order by fecha_vencimiento desc limit 1", nativeQuery = true)
    public Token findByCodTransaccionFirma(String codTransaccion);
    
    @Query(value = "select * from token where codigo_transaccion = ?1", nativeQuery = true)
    public List<Token> findByCodTransaccionUnico(String codTransaccion);

    @Query(value = "select t.* from token t inner join usuario u2 on t.id_usuario = u2.id_usuario where u2.correo_electronico = ?1 and t.activo = true and t.tipo = ?2 order by t.fecha_registro desc ", nativeQuery = true)
	public List<Token> consultarTokenUsuario(String email, int tipoToken);

	@Query(value = "select * from token t  where t.ids like ?2 and t.email_firmante = ?1 and id_usuario = ?3", nativeQuery = true)
	public Token findByIdsFirmante(String emailFirmante, String idArchivo, int idPropietario);
	
	@Query(value = "select * from token t  where t.email_firmante = ?1 and t.tipo = ?2 and t.activo =true", nativeQuery = true)
	public List<Token> findTokensByFirmante(String email, int tipoToken);

    public List<Token> findAllByActivoTrueAndTipo(int tipo);

    @Query(value = "SELECT * FROM token WHERE email_firmante = ?1 AND ids LIKE ?2 AND activo = 1 LIMIT 1", nativeQuery = true)
    Token buscarTokenPorEmailYIdArchivo(String emailFirmante, String pattern);
}
