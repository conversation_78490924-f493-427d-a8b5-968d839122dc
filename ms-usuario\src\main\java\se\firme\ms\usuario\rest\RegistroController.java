/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.rest;

import co.venko.ms.models.entity.AdmUsuario;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.models.projection.IUsuario;
import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.usuario.negocio.UsuarioNegocio;
import se.firme.ms.usuario.rest.client.UsuarioVMSClient;

/**
 * @document RegistroController
 * <AUTHOR> Machado Jaimes
 * @fecha martes, agosto 18 de 2020, 12:19:06 PM
 */
@RestController
@RefreshScope
@RequestMapping("/usuario")
public class RegistroController {

    @Autowired
    private UsuarioNegocio usuarioNegocioService;

    @Autowired
    private UsuarioVMSClient usuarioVMSClient;
	@Autowired
	private HttpServletRequest request;

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> registrar(@RequestBody RegistroDTO datosRegistro) {
        try {
            usuarioNegocioService.procesoRegistro(datosRegistro);
            return new ResponseEntity<>(new ApiResponse.ok().data("Se ha enviado un EMail a la dirección de correo electrónico especificada para proceder con la confirmación del registro").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> completar(@RequestBody DocumentoDTO documento) {
        try {
            usuarioNegocioService.completarRegistro(documento);
            return new ResponseEntity<>(new ApiResponse.ok().data("Registro exitoso").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/guardar-usuario-adm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> guardarUsuarioAdmi(@RequestBody AdmUsuario usuario) {
        try {
            usuarioVMSClient.guardarUsuarioAdm(usuario);
            return new ResponseEntity<>(new ApiResponse.ok().data("").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/servicio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarSmsMultiple(@RequestParam("codigo") long codigo) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data(usuarioNegocioService.getUsuarioServicio(codigo))
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/add-package", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> agregarPaquete(@RequestBody String usuario) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(usuarioNegocioService.agregarPaqueteServicio(usuario)).build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping(path = "/pwd/{cod}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> cambiarContrasena(@PathVariable(name = "cod") long cod,@RequestBody String body) {
        try {
        	System.out.println("Entra con código: "+cod);
        	usuarioNegocioService.cambiarContrasena(cod,body);
            return new ResponseEntity<>("{\"cod\": \""+cod+"\"}", HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>("{\"cod\": \""+cod+"\",\"message\": \""+e.getMessage()+"\"}", HttpStatus.BAD_REQUEST);
        }
    }
    
    @PostMapping(path = "/info/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> consultar(@PathVariable(name = "id") String id) {
    	 try {
    		IUsuario usuario= usuarioNegocioService.consultarUsuario(id);
            if(usuario!=null) {
            	 return new ResponseEntity<>(new ApiResponse.ok().data(usuario).build(), HttpStatus.OK);
            }
          
           
            return new ResponseEntity<>(new ApiResponse.ok().data("No hay registros ").build(), HttpStatus.OK);
         } catch (Exception e) {
             return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
         }
    }
    
    @PutMapping(path = "/servicio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> actualizarServicio(@RequestBody ServicioDTO dto) {
        try {
        	usuarioNegocioService.actualizarServicio(dto);
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data("Datos de servicio actualizado correctamente")
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
    
    @DeleteMapping(path = "/eliminar-cuenta", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> eliminarCuenta() {
        try {
        	
        	usuarioNegocioService.eliminarCuenta(Integer.parseInt(request.getHeader("X-USER-ID")));
            return new ResponseEntity<>(new ApiResponse.ok()
                    .data("Cuenta de usuario eliminada")
                    .build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping(path = "/buscar-documento-email/{docOrEmail}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> findByNumeroDeDocumentoOEmail(@PathVariable(name = "docOrEmail") String docOrEmail) {
        try {
            IUsuarioOris usuario = usuarioNegocioService.findByNumeroDeDocumentoOEmail(docOrEmail);

            if (usuario == null) {
                return new ResponseEntity<>(new ApiResponse.error().mensaje("Usuario no encontrado").build(), HttpStatus.NOT_FOUND);
            }

            return new ResponseEntity<>(new ApiResponse.ok().data(usuario).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
}


//se.firme.ms.usuario.models.service
