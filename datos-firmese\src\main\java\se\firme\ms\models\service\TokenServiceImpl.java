/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.ITokenDao;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.interfaz.ITokenService;

/**
 * <AUTHOR>
 */
@Service
public class TokenServiceImpl implements ITokenService {
//public class TokenServiceImpl implements ITokenService {

    @Autowired
    private ITokenDao tokenDao;
    @Autowired
    private IUsuarioDao usuarioDao;
    private static Logger logger = Logger.getLogger(TokenServiceImpl.class.getName());

    @Override
    @Transactional
    public Token getNuevoToken(Usuario usuario, int tipoToken, int longitud, Long idArhivoFirma, String emailFirmante,
                               String ids) {
        return getNuevoToken(usuario, tipoToken, longitud, idArhivoFirma, emailFirmante, ids, 1024);
    }

    public Token getNuevoToken(Usuario usuario, int tipoToken, int longitud, Long idArhivoFirma, String emailFirmante,
                               String ids, Date fechaVigencia) throws FirmaException {
        long totalHoras = 0;
        if (fechaVigencia != null) {
            Date fecha = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fechaVigencia);
            calendar.add(Calendar.HOUR_OF_DAY, 24);
            fechaVigencia = calendar.getTime();
            if (fechaVigencia.before(fecha)) {
                throw new FirmaException("La fecha de vigencia debe ser mayor a la fecha actual");
            }

            logger.info("Fecha vigencia: " + fechaVigencia);
            totalHoras = System.currentTimeMillis();
            totalHoras = fechaVigencia.getTime() - totalHoras;
            totalHoras = totalHoras / 1000 / 60 / 60;
        } else {
            totalHoras = 2160;
        }
        return getNuevoToken(usuario, tipoToken, longitud, idArhivoFirma, emailFirmante, ids, (int) totalHoras);
    }

    public Token getNuevoToken(Usuario usuario, int tipoToken, int longitud, Long idArhivoFirma, String emailFirmante,
                               String ids, int totalHoras) {
        logger.info("Total horas de vigencia de token: " + totalHoras);
        Token tkn = new Token();
        String token = Utilities.generarToken(longitud);
        Date fecha = new Date();
        tkn.setIdToken(token);
        tkn.setIdUsuario(usuario);
        tkn.setFechaRegistro(fecha);
        tkn.setTipo(tipoToken);
        tkn.setFechaVencimiento(Utilities.getSumarMinutosHorasDiasAFecha(fecha, 0, totalHoras, 0));
        tkn.setActivo(true);
        tkn.setCodigoTransaccion(getCodigoTransaccion(6));
        tkn.setIdArchivoFirma(idArhivoFirma);
        tkn.setEmailFirmante(emailFirmante);
        tkn.setIds(ids);
        tkn = tokenDao.save(tkn);

        return tkn;
    }

    private String getCodigoTransaccion(int longitud) {
        // Siempre retornar exactamente 6 numerales
        return "######";
    }

    @Transactional
    public Token consultarToken(String codigoToken) throws FirmaException {
        try {
            Token token = tokenDao.findByCode(codigoToken);
            if (token != null) {
                Date date = new Date();
                if (token.getActivo()) {
                    logger.log(Level.INFO, "Fecha de vencimiento: {0}", token.getFechaVencimiento());
                    if (date.before(token.getFechaVencimiento())) {
                        return token;
                    }
                    throw new FirmaException("Token supera la fecha de vencimiento");
                }
                throw new FirmaException("Token no está activo");
            }
            throw new FirmaException("No existe token con el código " + codigoToken);
        } catch (FirmaException | NullPointerException e) {
            logger.log(Level.SEVERE, "Error: {0}", "Token: " + codigoToken + " : " + e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    @Transactional
    public void addCodeSms(String codSms, String id_token) throws FirmaException {
        try {
            tokenDao.addCodeSms(codSms, id_token);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    @Transactional
    public void desactivarToken(String ctoken) throws FirmaException {
        try {
            tokenDao.desactivarToken(ctoken);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    @Transactional
    public Token consultarCodTransaccion(String codigo) throws FirmaException {
        try {
            Token token = tokenDao.findByCodTransaccion(codigo);
            if (token != null) {
                Date date = new Date();
                if (token.getActivo()) {
                    logger.log(Level.INFO, "Fecha de vencimiento: {0}", token.getFechaVencimiento());
                    if (date.before(token.getFechaVencimiento())) {
                        return token;
                    }
                    throw new FirmaException("Token supera la fecha de vencimiento");
                }
                throw new FirmaException("Token no está activo");
            }
            throw new FirmaException("No existe token con el código " + codigo);
        } catch (FirmaException | NullPointerException e) {
            logger.log(Level.SEVERE, "Error: {0}", "Token: " + codigo + " : " + e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    @Transactional
    public Token consultarCodTransaccionFirma(String codigo) throws FirmaException {
        try {
            Token token = tokenDao.findByCodTransaccionFirma(codigo);
            if (token != null) {
                Date date = new Date();
                if (token.getActivo()) {
                    logger.log(Level.INFO, "Fecha de vencimiento: {0}", token.getFechaVencimiento());
                    if (date.before(token.getFechaVencimiento())) {
                        return token;
                    }
                    throw new FirmaException("Token supera la fecha de vencimiento");
                }
                throw new FirmaException("Token no está activo");
            }
            throw new FirmaException("No existe token con el código " + codigo);
        } catch (FirmaException | NullPointerException e) {
            logger.log(Level.SEVERE, "Error: {0}", "Token: " + codigo + " : " + e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    @Override
    public Token findByIdArchivoFirma(long idArchivoFirma, long idUsuario) {
        return tokenDao.findByIdArchivoFirma(idArchivoFirma, idUsuario);
    }

    @Override
    @Transactional
    public boolean isTokenActivo(Token token) throws FirmaException {
        if (token != null) {
            Date date = new Date();
            if (token.getActivo()) {
                logger.log(Level.INFO, "Fecha de vencimiento: {0}", token.getFechaVencimiento());
                if (date.before(token.getFechaVencimiento())) {
                    return true;
                } else {
                    // inactivar token
                    desactivarTokenByIdToken(token.getIdToken());
                    return false;
                }
            }
            return false;
        } else {
            return false;
        }
    }

    @Override
    @Transactional
    public void desactivarTokenByIdToken(String idToken) {
        tokenDao.desactivarTokenByIdToken(idToken);
    }

    @Override
    public List<Token> findByCodTransaccionUnico(String codTransaccion) {
        return tokenDao.findByCodTransaccionUnico(codTransaccion);
    }

    @Override
    public Token getTokenActivoUsuario(Usuario usuario) {
        Token tkn = null;

        List<Token> tokens = tokenDao.findTokenActivoUsuario(usuario.getIdUsuario());

        if (tokens != null && !tokens.isEmpty()) {
            tkn = tokens.get(0);
        }

        if (tkn == null) {
            tkn = getNuevoToken(usuario, Parameters.string.TOKEN_TIPO_REGISTRO_USUARIO, 30, null, null, null);
        } else {
            Date date = new Date();
            if (date.before(tkn.getFechaVencimiento())) {
                return tkn;
            } else {
                return getNuevoToken(usuario, Parameters.string.TOKEN_TIPO_REGISTRO_USUARIO, 30, null, null, null);
            }
        }

        return tkn;
    }

    @Transactional
    public Token consultarTokenUsuario(String email, int tipoToken) throws FirmaException {
        try {
            List<Token> tokens = tokenDao.consultarTokenUsuario(email, tipoToken);
            if (tokens != null && !tokens.isEmpty()) {
                if (isTokenActivo(tokens.get(0))) {
                    return tokens.get(0);
                }
                return null;
            }
            return null;
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    public String crearTokenFirmante(long idUser, String ids, String email, Date fechaVigencia) throws FirmaException {
        try {
            logger.info("=== DEBUG CREAR TOKEN FIRMANTE ===");
            logger.info("IDs recibidos: [" + ids + "]");
            logger.info("Email: " + email);
            
            Optional<Usuario> usuario = usuarioDao.findById(idUser);
            if (usuario.isPresent()) {
                
                // VERIFICAR Y LIMPIAR EL FORMATO DE IDs
                String idsFinales = ids;
                
                // Si los IDs NO tienen el formato correcto, agregarlo
                if (!ids.startsWith("0-") || !ids.endsWith("-0")) {
                    // Solo formatear si no está ya formateado
                    idsFinales = "0-" + ids + "-0";
                    logger.info("IDs formateados desde: [" + ids + "] a: [" + idsFinales + "]");
                } else {
                    logger.info("IDs ya tienen formato correcto: [" + idsFinales + "]");
                }
                
                // ❌ NO HACER ESTO (línea 263 original):
                // Token token = getNuevoToken(..., "0" + ids + "0", ...);
                
                // ✅ HACER ESTO:
                Token token = getNuevoToken(usuario.get(), Parameters.string.TOKEN_TIPO_FIRMA_DOCUMENTO, 120, null,
                        email, idsFinales, fechaVigencia);
                
                if (token != null) {
                    logger.info("Token creado exitosamente con IDs finales: [" + idsFinales + "]");
                    return token.getIdToken();
                }
                throw new FirmaException("No se pudo generar el token para firma de documentos");
            }
            throw new FirmaException("Usuario no existe");
        } catch (Exception e) {
            logger.severe("Error while creating token with creator id: " + idUser + " for signers: " + ids + " email: " + email + " :: " + e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    /**
     * @param codigoToken
     * @return
     * @throws FirmaException
     */
    public Token findTokenByID(String codigoToken) throws FirmaException {
        try {
            Token token = tokenDao.findByCode(codigoToken);
            if (token != null) {
                return token;
            }
            throw new FirmaException("No existe token con el código " + codigoToken);
        } catch (FirmaException | NullPointerException e) {
            logger.log(Level.SEVERE, "Error: {0}", e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    public Token findByIdsFirmante(String emailFirmante, String idArchivo, int idPropietario) {
        return tokenDao.findByIdsFirmante(emailFirmante, idArchivo, idPropietario);
    }

    public boolean actualizarRegistro(Token tkn) throws FirmaException {
        try {
            tokenDao.save(tkn);
            return true;
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    public List<Token> findTokensByUsuario(String email, int tipoToken) throws FirmaException {
        try {
            return tokenDao.consultarTokenUsuario(email, tipoToken);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    public List<Token> findTokensByFirmante(String correoElectronico, int tipoToken) throws FirmaException {
        try {
            return tokenDao.findTokensByFirmante(correoElectronico, tipoToken);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    public Token buscarTokenPorEmailYIdArchivo(String emailFirmante, Long idArchivoFirma) {
        try {
            String pattern = "%" + idArchivoFirma + "%";
            return tokenDao.buscarTokenPorEmailYIdArchivo(emailFirmante, pattern);
        } catch (Exception e) {
            Logger.getLogger(TokenServiceImpl.class.getName()).severe("Error buscando token por email e idArchivo: " + e.getMessage());
            return null;
        }
    }
}
